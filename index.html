<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>幸运抽奖</title>
    <style>
      :root {
        --bg: #0f172a;
        --panel: #111827;
        --panel-2: #0b1220;
        --muted: #94a3b8;
        --text: #e5e7eb;
        --primary: #22d3ee;
        --primary-weak: rgba(34, 211, 238, 0.15);
        --accent: #a78bfa;
        --danger: #f43f5e;
        --success: #34d399;
        --shadow: 0 10px 30px rgba(0,0,0,0.35);
        --radius: 14px;
        --chip: #1f2937;
      }

      * { box-sizing: border-box; }
      html, body { height: 100%; }
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Noto Sans CJK SC", sans-serif;
        color: var(--text);
        background: radial-gradient(1200px 600px at 20% -10%, #1f2937 0%, transparent 60%),
                    radial-gradient(1000px 600px at 120% 10%, #0e7490 0%, transparent 50%),
                    linear-gradient(180deg, #0b1020 0%, #0a0f1c 100%);
        background-color: var(--bg);
      }

      .container {
        max-width: 1100px;
        margin: 48px auto;
        padding: 0 20px;
      }

      header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        gap: 12px;
      }

      .title {
        display: flex;
        align-items: center;
        gap: 12px;
      }
      .logo {
        width: 38px; height: 38px;
        border-radius: 12px;
        background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
        box-shadow: 0 6px 20px rgba(34, 211, 238, 0.35);
      }
      h1 {
        font-size: 22px;
        font-weight: 700;
        margin: 0;
        letter-spacing: 0.3px;
      }
      .subtitle { color: var(--muted); font-size: 13px; }

      .grid {
        display: grid;
        grid-template-columns: 1.1fr 1.2fr;
        gap: 20px;
      }

      .card {
        background: linear-gradient(180deg, rgba(255,255,255,0.04), rgba(255,255,255,0.02));
        border: 1px solid rgba(255,255,255,0.08);
        border-radius: var(--radius);
        box-shadow: var(--shadow);
      }

      .panel { padding: 16px; }
      .panel h2 { margin: 0 0 12px; font-size: 16px; }

      textarea {
        width: 100%;
        min-height: 200px;
        resize: vertical;
        padding: 12px 12px;
        border-radius: 10px;
        background: #0b1220;
        color: var(--text);
        border: 1px solid rgba(255,255,255,0.06);
        outline: none;
        font-size: 14px;
        line-height: 1.6;
        transition: border-color .2s, box-shadow .2s;
      }
      textarea:focus { border-color: rgba(34, 211, 238, 0.5); box-shadow: 0 0 0 4px rgba(34,211,238,0.12); }

      .row { display: flex; gap: 10px; align-items: center; flex-wrap: wrap; }
      .row + .row { margin-top: 10px; }

      .control {
        display: flex; align-items: center; gap: 8px;
        background: rgba(255,255,255,0.03);
        border: 1px solid rgba(255,255,255,0.08);
        padding: 8px 10px;
        border-radius: 10px;
      }
      .control label { font-size: 13px; color: var(--muted); }
      .control input[type="number"] {
        width: 90px; padding: 8px 10px; border-radius: 8px; border: 1px solid rgba(255,255,255,0.08);
        background: #0b1220; color: var(--text); font-size: 14px; outline: none;
      }

      .btn {
        appearance: none; border: none; outline: none; cursor: pointer;
        padding: 10px 14px; border-radius: 10px; font-size: 14px; font-weight: 600;
        color: #0b1220; background: var(--primary); box-shadow: 0 8px 22px rgba(34,211,238,0.35);
        transition: transform .06s ease, box-shadow .2s ease, background .2s ease;
      }
      .btn:hover { transform: translateY(-1px); }
      .btn:active { transform: translateY(0); box-shadow: 0 6px 14px rgba(34,211,238,0.3); }

      .btn.secondary { background: #1f2937; color: var(--text); box-shadow: none; border: 1px solid rgba(255,255,255,0.08); }
      .btn.danger { background: var(--danger); color: white; box-shadow: 0 8px 22px rgba(244,63,94,0.35); }
      .btn.success { background: var(--success); color: #062018; box-shadow: 0 8px 22px rgba(52,211,153,0.35); }
      .btn[disabled] { opacity: .5; cursor: not-allowed; transform: none; box-shadow: none; }

      .stats { display: flex; gap: 8px; flex-wrap: wrap; margin-top: 8px; }
      .stat {
        font-size: 12px; color: var(--muted);
        background: rgba(255,255,255,0.03);
        border: 1px solid rgba(255,255,255,0.08);
        padding: 6px 10px;
        border-radius: 999px;
      }

      /* 右侧展示 */
      .display {
        padding: 16px;
        display: flex; flex-direction: column; gap: 12px;
      }

      .screen {
        position: relative;
        background: radial-gradient(600px 180px at 50% 20%, rgba(34,211,238,0.08) 0%, transparent 70%),
                    linear-gradient(180deg, rgba(255,255,255,0.04), rgba(255,255,255,0.02));
        border: 1px solid rgba(255,255,255,0.08);
        border-radius: 16px;
        padding: 22px 16px;
        min-height: 160px;
        display: grid; place-items: center;
        overflow: hidden;
      }
      .rolling {
        font-size: clamp(22px, 3.2vw, 34px);
        font-weight: 800;
        letter-spacing: 0.4px;
        text-align: center;
        color: #eafcff;
        text-shadow: 0 6px 26px rgba(34,211,238,0.25);
        animation: pulse 1.2s ease-in-out infinite;
        user-select: none;
      }
      .rolling.dim { opacity: .5; animation: none; text-shadow: none; }
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.03); }
        100% { transform: scale(1); }
      }

      .hint { position: absolute; inset: auto 12px 10px 12px; text-align: center; color: var(--muted); font-size: 12px; }

      .results {
        background: rgba(255,255,255,0.03);
        border: 1px solid rgba(255,255,255,0.08);
        border-radius: 14px;
        padding: 12px;
      }
      .results h3 { margin: 0 0 10px; font-size: 15px; }

      .chips { display: flex; flex-wrap: wrap; gap: 8px; }
      .chip {
        background: var(--chip);
        border: 1px solid rgba(255,255,255,0.08);
        padding: 8px 10px;
        border-radius: 999px;
        font-size: 13px;
        display: inline-flex; align-items: center; gap: 6px;
      }
      .chip .dot { width: 6px; height: 6px; border-radius: 50%; background: var(--primary); box-shadow: 0 0 0 3px var(--primary-weak); }

      .history { margin-top: 10px; display: grid; gap: 8px; max-height: 240px; overflow: auto; }
      .round { padding: 10px; border-radius: 10px; background: rgba(255,255,255,0.02); border: 1px solid rgba(255,255,255,0.08); }
      .round-title { font-weight: 600; font-size: 13px; color: var(--muted); margin-bottom: 6px; }

      .toast { display: none; position: fixed; left: 50%; top: 18px; transform: translateX(-50%);
        background: #0b1220; color: var(--text); border: 1px solid rgba(255,255,255,0.08); padding: 10px 14px; border-radius: 10px; box-shadow: var(--shadow);
        font-size: 13px; z-index: 50; }
      .toast.show { display: inline-block; animation: fade 2.4s forwards; }
      @keyframes fade { 0% { opacity: 0; transform: translate(-50%, -6px); } 10% { opacity: 1; transform: translate(-50%, 0); } 85% { opacity: 1; } 100% { opacity: 0; } }

      /* 礼花动画效果 */
      .fireworks-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
        overflow: hidden;
      }

      .firework {
        position: absolute;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        animation: firework-explosion 1.5s ease-out forwards;
      }

      @keyframes firework-explosion {
        0% {
          transform: scale(0) rotate(0deg);
          opacity: 1;
        }
        50% {
          transform: scale(1.5) rotate(180deg);
          opacity: 0.8;
        }
        100% {
          transform: scale(0.5) rotate(360deg);
          opacity: 0;
        }
      }

      .firework-particle {
        position: absolute;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        animation: particle-fly 2s ease-out forwards;
      }

      @keyframes particle-fly {
        0% {
          transform: translate(0, 0) scale(1);
          opacity: 1;
        }
        100% {
          transform: translate(var(--dx), var(--dy)) scale(0);
          opacity: 0;
        }
      }

      .congratulations {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1, #96ceb4);
        background-size: 300% 300%;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: clamp(32px, 6vw, 64px);
        font-weight: 900;
        text-align: center;
        z-index: 1001;
        pointer-events: none;
        animation: rainbow-text 2s ease-in-out, congratulations-bounce 1.5s ease-out;
        text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
      }

      @keyframes rainbow-text {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }

      @keyframes congratulations-bounce {
        0% {
          transform: translate(-50%, -50%) scale(0) rotate(-10deg);
          opacity: 0;
        }
        50% {
          transform: translate(-50%, -50%) scale(1.2) rotate(5deg);
          opacity: 1;
        }
        70% {
          transform: translate(-50%, -50%) scale(0.9) rotate(-2deg);
        }
        100% {
          transform: translate(-50%, -50%) scale(1) rotate(0deg);
          opacity: 1;
        }
      }

      @media (max-width: 900px) {
        .grid { grid-template-columns: 1fr; }
        .container { margin: 28px auto; }
      }
    </style>
  </head>
  <body>
    <div class="toast" id="toast" role="status" aria-live="polite"></div>
    <div class="fireworks-container" id="fireworksContainer"></div>
    <div class="container">
      <header>
        <div class="title">
          <div class="logo" aria-hidden="true"></div>
          <div>
            <h1>幸运抽奖</h1>
            <div class="subtitle">纯前端实现 · 支持多次抽取 · 自动剔除已中奖</div>
          </div>
        </div>
      </header>

      <div class="grid">
        <!-- 左侧：名单与控制 -->
        <section class="card panel" aria-labelledby="settingsTitle">
          <h2 id="settingsTitle">参与名单</h2>
          <textarea id="nameInput" placeholder="在此输入参与者名单，每行一个名字。例如：
张三
李四
王五
..."></textarea>

          <div class="row" style="margin-top: 12px;">
            <div class="control" aria-label="每次抽取人数">
              <label for="countInput">每次人数</label>
              <input id="countInput" type="number" min="1" value="1" />
            </div>

            <button id="startBtn" class="btn" type="button">开始抽奖</button>
            <button id="stopBtn" class="btn success" type="button" disabled>停止抽奖</button>
            <button id="resetBtn" class="btn secondary" type="button">重置</button>
          </div>

          <div class="stats" id="stats">
            <div class="stat">总人数：<strong id="statTotal">0</strong></div>
            <div class="stat">剩余：<strong id="statRemain">0</strong></div>
            <div class="stat">已抽出：<strong id="statDrawn">0</strong></div>
          </div>
        </section>

        <!-- 右侧：展示区与历史结果 -->
        <section class="card display" aria-labelledby="displayTitle">
          <h2 id="displayTitle" style="margin: 0;">抽奖展示</h2>

          <div class="screen">
            <div id="rollingText" class="rolling dim">准备就绪，点击「开始抽奖」</div>
            <div class="hint">抽奖进行中会滚动显示候选人，点击「停止抽奖」即可锁定中奖名单</div>
          </div>

          <div class="results" aria-live="polite">
            <h3>本轮中奖</h3>
            <div id="currentWinners" class="chips"></div>
          </div>

          <div class="results">
            <h3>历史记录</h3>
            <div id="history" class="history" aria-live="polite"></div>
          </div>
        </section>
      </div>
    </div>

    <script>
      // -------------------- 状态与工具函数 --------------------
      /** 全量名单（去重、净化后的最新版本） */
      let allParticipants = [];
      /** 剩余可抽取池 */
      let remainingPool = [];
      /** 已抽出（按出现顺序记录） */
      let drawnList = [];
      /** 抽奖计时器与状态 */
      let rollingTimer = null;
      let isRolling = false;
      /** 历史轮次 */
      let roundIndex = 0;

      const nameInput = document.getElementById('nameInput');
      const countInput = document.getElementById('countInput');
      const startBtn = document.getElementById('startBtn');
      const stopBtn = document.getElementById('stopBtn');
      const resetBtn = document.getElementById('resetBtn');
      const statTotal = document.getElementById('statTotal');
      const statRemain = document.getElementById('statRemain');
      const statDrawn = document.getElementById('statDrawn');
      const rollingText = document.getElementById('rollingText');
      const currentWinnersEl = document.getElementById('currentWinners');
      const historyEl = document.getElementById('history');
      const toastEl = document.getElementById('toast');
      const fireworksContainer = document.getElementById('fireworksContainer');

      function toast(message) {
        toastEl.textContent = message;
        toastEl.classList.remove('show');
        // 强制 reflow 以重启动画
        void toastEl.offsetWidth;
        toastEl.classList.add('show');
      }

      function sanitizeNames(raw) {
        return raw
          .split(/\r?\n/)
          .map(s => s.trim())
          .filter(s => s.length > 0);
      }

      function uniqueOrder(names) {
        const seen = new Set();
        const result = [];
        for (const n of names) {
          if (!seen.has(n)) { seen.add(n); result.push(n); }
        }
        return result;
      }

      function updateStats() {
        statTotal.textContent = String(allParticipants.length);
        statRemain.textContent = String(remainingPool.length);
        statDrawn.textContent = String(drawnList.length);
      }

      function setRollingUI(active) {
        isRolling = active;
        startBtn.disabled = active || remainingPool.length === 0;
        stopBtn.disabled = !active;
        nameInput.disabled = active;
        countInput.disabled = active;
        rollingText.classList.toggle('dim', !active);
      }

      function ensurePoolFromTextarea() {
        const parsed = uniqueOrder(sanitizeNames(nameInput.value));
        if (parsed.length === 0) {
          allParticipants = [];
          remainingPool = [];
          drawnList = [];
          roundIndex = 0;
          updateStats();
          return false;
        }
        allParticipants = parsed;
        // 如果已经有历史抽取，保持已抽出不变，从全量里剔除；否则全量复制到剩余池
        const drawnSet = new Set(drawnList);
        remainingPool = allParticipants.filter(n => !drawnSet.has(n));
        updateStats();
        return true;
      }

      function sampleUnique(list, k) {
        // Fisher-Yates 打乱复制
        const arr = list.slice();
        for (let i = arr.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [arr[i], arr[j]] = [arr[j], arr[i]];
        }
        return arr.slice(0, k);
      }

      function renderChips(container, names) {
        container.innerHTML = '';
        names.forEach(n => {
          const chip = document.createElement('span');
          chip.className = 'chip';
          const dot = document.createElement('span');
          dot.className = 'dot';
          const text = document.createElement('span');
          text.textContent = n;
          chip.appendChild(dot);
          chip.appendChild(text);
          container.appendChild(chip);
        });
      }

      function addHistoryRound(names) {
        const round = document.createElement('div');
        round.className = 'round';
        const title = document.createElement('div');
        title.className = 'round-title';
        title.textContent = `第 ${roundIndex} 轮`;
        const chips = document.createElement('div');
        chips.className = 'chips';
        renderChips(chips, names);
        round.appendChild(title);
        round.appendChild(chips);
        historyEl.prepend(round);
      }

      // 礼花动画函数
      function createFirework(x, y) {
        const colors = ['#ff6b6b', '#ffd93d', '#6bcf7f', '#4ecdc4', '#45b7d1', '#96ceb4', '#ff8a80', '#ffb74d'];
        const particleCount = 12;
        
        for (let i = 0; i < particleCount; i++) {
          const particle = document.createElement('div');
          particle.className = 'firework-particle';
          
          const color = colors[Math.floor(Math.random() * colors.length)];
          particle.style.backgroundColor = color;
          particle.style.boxShadow = `0 0 6px ${color}`;
          
          const angle = (i / particleCount) * 2 * Math.PI;
          const distance = 80 + Math.random() * 40;
          const dx = Math.cos(angle) * distance;
          const dy = Math.sin(angle) * distance;
          
          particle.style.left = x + 'px';
          particle.style.top = y + 'px';
          particle.style.setProperty('--dx', dx + 'px');
          particle.style.setProperty('--dy', dy + 'px');
          
          fireworksContainer.appendChild(particle);
          
          // 2秒后移除粒子
          setTimeout(() => {
            if (particle.parentNode) {
              particle.parentNode.removeChild(particle);
            }
          }, 2000);
        }
      }

      function launchFireworks() {
        const fireworkCount = 6;
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        
        for (let i = 0; i < fireworkCount; i++) {
          setTimeout(() => {
            const x = Math.random() * screenWidth;
            const y = Math.random() * (screenHeight * 0.6) + screenHeight * 0.1;
            createFirework(x, y);
          }, i * 200);
        }
      }

      function showCongratulations(winners) {
        const congratsEl = document.createElement('div');
        congratsEl.className = 'congratulations';
        congratsEl.textContent = `🎉 恭喜 ${winners.join('、')} 中奖！🎉`;
        
        document.body.appendChild(congratsEl);
        
        // 3秒后移除恭喜文字
        setTimeout(() => {
          if (congratsEl.parentNode) {
            congratsEl.parentNode.removeChild(congratsEl);
          }
        }, 3000);
      }

      // -------------------- 事件绑定 --------------------
      startBtn.addEventListener('click', () => {
        // 准备名单
        const ok = ensurePoolFromTextarea();
        if (!ok) {
          toast('请先输入参与者名单');
          rollingText.textContent = '请在左侧输入名单后开始抽奖';
          return;
        }
        const want = Math.max(1, Number(countInput.value) || 1);
        if (remainingPool.length === 0) {
          toast('没有可抽取的人员');
          return;
        }
        if (want > remainingPool.length) {
          toast(`人数不足，已自动改为 ${remainingPool.length}`);
          countInput.value = String(remainingPool.length);
        }

        setRollingUI(true);
        // 滚动动画：从剩余池中快速取名展示
        let idx = 0;
        rollingTimer = setInterval(() => {
          if (remainingPool.length === 0) return;
          idx = (idx + 1) % remainingPool.length;
          const name = remainingPool[Math.floor(Math.random() * remainingPool.length)];
          rollingText.textContent = name;
        }, 60);
      });

      stopBtn.addEventListener('click', () => {
        if (!isRolling) return;
        clearInterval(rollingTimer);
        rollingTimer = null;
        setRollingUI(false);

        const want = Math.max(1, Number(countInput.value) || 1);
        const k = Math.min(want, remainingPool.length);
        const winners = sampleUnique(remainingPool, k);

        // 从池中剔除，加入已抽出
        const winSet = new Set(winners);
        remainingPool = remainingPool.filter(n => !winSet.has(n));
        drawnList.push(...winners);

        // 渲染
        roundIndex += 1;
        renderChips(currentWinnersEl, winners);
        addHistoryRound(winners);
        rollingText.textContent = winners.join('、');
        updateStats();

        // 触发礼花动画和恭喜效果
        setTimeout(() => {
          launchFireworks();
          showCongratulations(winners);
        }, 300);

        if (remainingPool.length === 0) {
          toast('抽取完毕，已无剩余人员');
        }
      });

      resetBtn.addEventListener('click', () => {
        if (isRolling) return;
        // 重置所有状态
        const names = uniqueOrder(sanitizeNames(nameInput.value));
        allParticipants = names;
        remainingPool = names.slice();
        drawnList = [];
        roundIndex = 0;
        currentWinnersEl.innerHTML = '';
        historyEl.innerHTML = '';
        rollingText.textContent = names.length ? '已重置，点击「开始抽奖」' : '准备就绪，点击「开始抽奖」';
        updateStats();
        toast('已重置');
      });

      // 初始化一些示例数据，便于直接体验
      const demo = ['张伟明', '李国强', '王建军', '刘志华', '陈建华', '杨晓东', '赵建国', '黄志强', '吴俊杰', '周华健', '徐文涛', '孙立军', '马骏峰', '朱建华', '胡志明', '郭建华', '林志远', '李志强', '马邦德', '郭靖'];
      nameInput.value = demo.join('\n');
      (function init() {
        const names = uniqueOrder(sanitizeNames(nameInput.value));
        allParticipants = names;
        remainingPool = names.slice();
        drawnList = [];
        roundIndex = 0;
        updateStats();
      })();
    </script>
  </body>
  </html>


